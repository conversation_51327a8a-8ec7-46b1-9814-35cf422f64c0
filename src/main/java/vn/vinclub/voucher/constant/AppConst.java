package vn.vinclub.voucher.constant;

import vn.vinclub.voucher.dto.LockData;

import java.util.concurrent.TimeUnit;

public interface AppConst {

    String HYPHEN = "-";
    String SLASH = "/";
    String UPDATED_BY_AUTO = "SYSTEM";

    Integer DEFAULT_QUERY_BATCH_SIZE = 1000;

    Integer DEFAULT_SYNC_BATCH_SIZE = 100;

    /**
     * language
     */
    interface Language {
        String VI = "vi";
        String EN = "en";
        String DEFAULT = "vi";
    }

    /**
     * Redis message
     */
    interface RedisMessage {
        String CHANGE_VOUCHER_PROVIDER = "CHANGE_VOUCHER_PROVIDER";
        String CHANGE_PROVIDER_MERCHANT = "CHANGE_PROVIDER_MERCHANT";
        String CHANGE_PROVIDER_VOUCHER_LISTING = "CHANGE_PROVIDER_VOUCHER_LISTING";
    }

    /**
     * Redis lock data
     */
    interface RedisLock {
        LockData VOUCHER_PURCHASE_REQUEST = LockData.builder()
                .lockName("voucher_svc:VOUCHER_PURCHASE_REQUEST_")
                .waitTime(20L)
                .leaseTime(60000L)
                .timeUnit(TimeUnit.SECONDS)
                .build();
    }

    interface IdGenerator {
        String PROVIDER_URBOX_TRANSACTION_ID = "urbox-transaction-id";
    }
}

package vn.vinclub.voucher.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedStatusChangeReqDto;
import vn.vinclub.voucher.model.VoucherPurchased;

import java.util.List;

public interface VoucherPurchasedService {
    void changeVoucherStatus(VoucherPurchasedStatusChangeReqDto statusChangeReqDto);

    List<VoucherPurchased> saveMultiple(List<VoucherPurchased> voucherPurchasedList);

    Page<VoucherPurchased> getAllByVoucherImportId(String voucherImportId, Pageable pageable);
    /**
     * Get a page of unique transaction IDs that have active purchased vouchers
     * @param providerCode the provider code
     * @param pageable pagination information
     * @return Page of unique transaction IDs
     */
    Page<String> getAllTransactionIdsWithActivePurchasedVouchers(String providerCode, Pageable pageable);
}

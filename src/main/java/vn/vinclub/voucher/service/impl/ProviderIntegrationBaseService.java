package vn.vinclub.voucher.service.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.SerializationCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.EncryptKeySetting;
import vn.vinclub.voucher.dto.VClubLock;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherPurchaseRequestEvent;
import vn.vinclub.voucher.dto.provider.ProviderCreateDto;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingDetailsDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingDto;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingFilterDto;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedStatusChangeReqDto;
import vn.vinclub.voucher.enums.VoucherPurchaseRequestStatusEnum;
import vn.vinclub.voucher.enums.VoucherPurchaseTransactionStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.exception.IgnoreProcessingException;
import vn.vinclub.voucher.mapper.ProviderMerchantMapper;
import vn.vinclub.voucher.mapper.ProviderVoucherListingMapper;
import vn.vinclub.voucher.model.*;
import vn.vinclub.voucher.service.*;
import vn.vinclub.voucher.util.CustomUtil;
import vn.vinclub.voucher.util.KeyUtil;

import java.security.PublicKey;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class ProviderIntegrationBaseService extends BaseService {

    private static final String PROVIDER_VOUCHER_LISTING_DETAILS_CACHE_KEY = "voucher_svc:listing_details_";

    private final Codec codec = new SerializationCodec();

    @Autowired
    protected VoucherPurchaseTransactionHistoryService voucherPurchaseTransactionHistoryService;

    @Autowired
    protected VoucherPurchaseRequestHistoryService voucherPurchaseRequestHistoryService;

    @Autowired
    protected ProviderVoucherListingService providerVoucherListingService;

    @Autowired
    protected VoucherPurchasedService voucherPurchasedService;

    @Autowired
    protected ProviderMerchantService providerMerchantService;

    @Autowired
    protected ProviderService providerService;

    @Autowired
    protected RedissonClient redissonClient;

    @PersistenceContext
    private EntityManager entityManager;

    protected void initProvider(String providerCode, String providerName, EncryptKeySetting encryptKeySetting) {
        log.info("|------------------------------ START INIT PROVIDER ------------------------------|");
        try (Profiler p = new Profiler(getClass(), "initProvider")) {
            if (!providerService.existsByCode(providerCode)) {
                var createdProvider = providerService.create(
                        ProviderCreateDto.builder()
                                .code(providerCode)
                                .name(providerName)
                                .encryptKeySetting(encryptKeySetting)
                                .build()
                );
                log.info("|-- Initial provider {} successfully", providerCode);
                PublicKey publicKey = KeyUtil.decodePublicKey(createdProvider.getEncryptionKey().getPublicKey());
                log.info("|-- Please share the public key as below to {}: \n{}", providerCode, KeyUtil.printFormatKey(publicKey));
            } else {
                log.info("|-- Provider {} already initialized!", providerCode);
            }
        } catch (Exception e) {
            log.error("|-- Error initializing provider {}: {}", providerCode, e.getMessage());
        }
        log.info("|------------------------------ END INIT PROVIDER ------------------------------|");
    }

    protected boolean isActiveProvider(String providerCode) {
        try {
            providerService.getActiveByCode(providerCode);
            return true;
        } catch (BusinessLogicException e) {
            return false;
        } catch (Exception e) {
            log.error("Error while checking if provider {} is active: {}", providerCode, e.getMessage());
            throw e;
        }
    }

    protected ProviderVoucherListingDetailsDto getProviderVoucherListingDetail(String providerCode, String providerVoucherListingId, Long voucherListingId) {
        try (Profiler p = new Profiler(getClass(), "getProviderVoucherListingDetail")) {
            var cacheKey = buildListingDetailCacheKey(providerCode, voucherListingId);
            RBucket<ProviderVoucherListingDetailsDto> cachedVoucherListingDetailBucket = redissonClient.getBucket(cacheKey, codec);
            var cachedVoucherListingDetail = cachedVoucherListingDetailBucket.get();
            if (Objects.nonNull(cachedVoucherListingDetail)) {
                try (Profiler ch = new Profiler(p, "cacheHit")) {
                    return cachedVoucherListingDetail;
                }
            }
            try (Profiler cm = new Profiler(p, "cacheMiss")) {
                var voucherListingDetail = fetchProviderVoucherListingDetailFromApi(providerVoucherListingId, voucherListingId);
                if (Objects.nonNull(voucherListingDetail)) {
                    cachedVoucherListingDetailBucket.set(voucherListingDetail, Duration.ofMinutes(60));
                }
                return voucherListingDetail;
            }
        }
    }

    protected void invalidateProviderVoucherListingDetailCache(String providerCode, Long voucherListingId) {
        var cacheKey = buildListingDetailCacheKey(providerCode, voucherListingId);
        redissonClient.getBucket(cacheKey, codec).delete();
    }

    private static String buildListingDetailCacheKey(String providerCode, Long voucherListingId) {
        return PROVIDER_VOUCHER_LISTING_DETAILS_CACHE_KEY + providerCode + "_" + voucherListingId;
    }

    protected void processPurchaseVoucher(ProviderVoucherPurchaseRequestEvent event, Integer maxQuantityPerRequest) {
        try (Profiler p = new Profiler(getClass(), "processPurchaseVoucher")) {
            var voucherPurchaseRequest = voucherPurchaseRequestHistoryService.findByVoucherImportId(event.getVoucherImportId());
            if (Objects.isNull(voucherPurchaseRequest)) {
                log.error("Voucher purchase request not found for import id: {}. Skipped!!!", event.getVoucherImportId());
                throw new IgnoreProcessingException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
            }
            if (VoucherPurchaseRequestStatusEnum.COMPLETED.equals(voucherPurchaseRequest.getStatus())) {
                log.info("Voucher purchase request {} has been completed. Skipped!!!", event.getVoucherImportId());
                throw new IgnoreProcessingException(AppErrorCode.EVENT_PROCESSED, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
            }

            try (var lock = new VClubLock(redissonClient, AppConst.RedisLock.VOUCHER_PURCHASE_REQUEST.withPostFix(voucherPurchaseRequest.getVoucherImportId()))) {
                // detach the voucher purchase request from the entity manager for get fresh data
                entityManager.detach(voucherPurchaseRequest);

                // Get the latest requestHistory of the voucher purchase request
                var requestHistory = voucherPurchaseRequestHistoryService.findByVoucherImportId(voucherPurchaseRequest.getVoucherImportId());
                if (Objects.isNull(requestHistory)) {
                    log.error("Voucher purchase request requestHistory not found for import id: {}. Skipped!!!", event.getVoucherImportId());
                    throw new IgnoreProcessingException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
                }
                if (VoucherPurchaseRequestStatusEnum.COMPLETED.equals(requestHistory.getStatus())) {
                    log.info("Voucher purchase request {} has been completed. Skipped!!!", event.getVoucherImportId());
                    throw new IgnoreProcessingException(AppErrorCode.EVENT_PROCESSED, VoucherPurchaseRequestHistory.Fields.voucherImportId, event.getVoucherImportId());
                }

                requestHistory = recheckLastProcessing(requestHistory);

                var isCompleted = false;
                var remainingQuantity = requestHistory.getRequestQuantity() - requestHistory.getPurchasedQuantity();
                var errorMessage = AppErrorCode.SYSTEM_ERROR.getMessage();
                var errorCode = AppErrorCode.SYSTEM_ERROR.getCode();

                do {
                    var requestQuantity = Math.min(remainingQuantity, maxQuantityPerRequest);

                    var txn = processProviderPurchaseTransaction(requestHistory, requestQuantity);

                    if (VoucherPurchaseTransactionStatusEnum.FAILED.equals(txn.getStatus())) {
                        log.error("Transaction {} failed with error: {}", txn.getTransactionId(), txn.getErrorMessage());
                        errorCode = txn.getErrorCode();
                        errorMessage = txn.getErrorMessage();
                        isCompleted = true;
                    } else {
                        var purchasedVouchers = extractPurchasedVouchers(txn);
                        requestHistory = voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
                        remainingQuantity -= txn.getQuantity();
                        if (remainingQuantity <= 0) {
                            isCompleted = true;
                        }
                    }
                } while (!isCompleted);

                voucherPurchaseRequestHistoryService.completePurchaseRequest(requestHistory, errorCode, errorMessage);
            }
        }
    }

    private VoucherPurchaseRequestHistory recheckLastProcessing(VoucherPurchaseRequestHistory requestHistory) {
        try (Profiler p = new Profiler(getClass(), "recheckProcessingRequestHistory")) {
            var lastTransactionList = voucherPurchaseTransactionHistoryService.getByVoucherImportId(requestHistory.getVoucherImportId());

            if (CollectionUtils.isEmpty(lastTransactionList)) {
                return requestHistory;
            }

            var latestTransaction = lastTransactionList.stream()
                    .max(Comparator.comparingLong(VoucherPurchaseTransactionHistory::getCreatedTime)).get();

            if (VoucherPurchaseTransactionStatusEnum.FAILED.equals(latestTransaction.getStatus())) {
                return requestHistory;
            }

            if (VoucherPurchaseTransactionStatusEnum.PROCESSING.equals(latestTransaction.getStatus())) {
                var rerunTransaction = rerunTransaction(latestTransaction);
                if (VoucherPurchaseTransactionStatusEnum.SUCCESS.equals(rerunTransaction.getStatus())) {
                    var purchasedVouchers = extractPurchasedVouchers(rerunTransaction);
                    return voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
                }
                return requestHistory;
            }

            if (VoucherPurchaseTransactionStatusEnum.SUCCESS.equals(latestTransaction.getStatus())) {
                var successQuantity = lastTransactionList.stream()
                        .filter(txn -> VoucherPurchaseTransactionStatusEnum.SUCCESS.equals(txn.getStatus()))
                        .mapToInt(VoucherPurchaseTransactionHistory::getQuantity)
                        .sum();

                // check if the success quantity is equal to the purchased quantity -> last transaction is completed update to request history
                if (Objects.equals(successQuantity, requestHistory.getPurchasedQuantity())) {
                    return requestHistory;
                }

                var purchasedVouchers = extractPurchasedVouchers(latestTransaction);
                return voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
            }

            throw new BusinessLogicException(AppErrorCode.SYSTEM_ERROR);
        }
    }

    protected List<VoucherPurchaseTransactionHistory> getProviderTimeoutTransactions(String providerCode) {
        try (Profiler p = new Profiler(getClass(), "getProviderTimeoutTransactions")) {
            return voucherPurchaseTransactionHistoryService.findByErrorCode(providerCode, AppErrorCode.EXTERNAL_API_CALL_TIMEOUT.getCode());
        }
    }

    protected void rerunTimeoutTransactions(List<VoucherPurchaseTransactionHistory> timeoutTransactions) {
        try (Profiler p = new Profiler(getClass(), "rerunTimeoutTransactions")) {
            // group by voucherImportId
            var groupedTransactions = new HashMap<String, List<VoucherPurchaseTransactionHistory>>();
            for (var txn : timeoutTransactions) {
                var voucherImportId = txn.getVoucherImportId();
                if (!groupedTransactions.containsKey(voucherImportId)) {
                    groupedTransactions.put(voucherImportId, new ArrayList<>());
                }
                groupedTransactions.get(voucherImportId).add(txn);
            }

            // process each group
            for (var entry : groupedTransactions.entrySet()) {
                var voucherImportId = entry.getKey();
                var transactions = entry.getValue();
                log.info("Retrying timeout transactions for voucherImportId: {}", voucherImportId);
                try (var lock = new VClubLock(redissonClient, AppConst.RedisLock.VOUCHER_PURCHASE_REQUEST.withPostFix(voucherImportId))) {
                    // Get the latest requestHistory of the voucher purchase request
                    var requestHistory = voucherPurchaseRequestHistoryService.findByVoucherImportId(voucherImportId);
                    if (Objects.isNull(requestHistory)) {
                        log.error("Voucher purchase request not found for voucherImportId: {}", voucherImportId);
                        continue;
                    }
                    if (VoucherPurchaseRequestStatusEnum.PROCESSING.equals(requestHistory.getStatus())) {
                        log.warn("Voucher purchase request is still processing, skipped retrying timeout transactions for voucherImportId: {}", voucherImportId);
                        continue;
                    }

                    var totalPurchasedQuantity = requestHistory.getPurchasedQuantity();

                    for (var txn : transactions) {
                        try {
                            var rerunTxn = rerunTransaction(txn);
                            if (VoucherPurchaseTransactionStatusEnum.FAILED.equals(rerunTxn.getStatus())) {
                                log.error("Rerun timeout transaction {} failed with error: {}", rerunTxn.getTransactionId(), rerunTxn.getErrorMessage());
                            } else {
                                var purchasedVouchers = extractPurchasedVouchers(rerunTxn);
                                requestHistory = voucherPurchaseRequestHistoryService.processPurchaseRequest(requestHistory, purchasedVouchers);
                                totalPurchasedQuantity += purchasedVouchers.size();
                            }
                        } catch (Exception e) {
                            log.error("Error while retrying timeout transaction {}", txn.getTransactionId(), e);
                        }
                    }
                    if (totalPurchasedQuantity > 0) {
                        voucherPurchaseRequestHistoryService.completePurchaseRequest(requestHistory, null, null);
                    }
                }
            }
        }
    }

    protected void checkAndUpdateOrCreateProviderMerchantByBatch(String providerCode, List<ProviderMerchantDto> providerMerchantByBatch) {
        try (Profiler p = new Profiler(getClass(), "checkAndUpdateOrCreateProviderMerchantByBatch")) {
            if (CollectionUtils.isEmpty(providerMerchantByBatch)) {
                log.info("|- No provider merchant to sync");
                return;
            }

            log.info("|- Check and update or create provider merchant by batch for provider {} with {} merchants", providerCode, providerMerchantByBatch.size());

            var providerMerchantCodes = providerMerchantByBatch.stream()
                    .map(ProviderMerchantDto::getProviderMerchantCode)
                    .collect(Collectors.toSet());

            var merchantByBatch = providerMerchantService.getAllByProviderCodeAndProviderMerchantCodes(
                    providerCode, providerMerchantCodes
            );

            var totalCreate = 0;
            var totalCreateFailed = 0;
            var totalUpdate = 0;
            var totalUpdateFailed = 0;

            // Check update or create
            for (var latestMerchant : providerMerchantByBatch) {
                var currentMerchant = merchantByBatch.stream()
                        .filter(merchant -> merchant.getProviderMerchantCode().equals(latestMerchant.getProviderMerchantCode()))
                        .findFirst().orElse(null);
                if (Objects.isNull(currentMerchant)) {
                    var createDto = ProviderMerchantMapper.INSTANCE.toCreateDto(latestMerchant);
                    createDto.setProviderCode(providerCode);
                    try {
                        providerMerchantService.create(createDto);
                        totalCreate++;
                    } catch (Exception e) {
                        log.error("|-- Error while creating provider merchant with code {}: {}", createDto.getProviderMerchantCode(), e.getMessage());
                        totalCreateFailed++;
                    }
                } else {
                    if (checkIfMerchantNeedUpdate(currentMerchant, latestMerchant)) {
                        var updateDto = ProviderMerchantMapper.INSTANCE.toUpdateDto(latestMerchant);
                        try {
                            providerMerchantService.update(currentMerchant.getId(), updateDto);
                            totalUpdate++;
                        } catch (Exception e) {
                            log.error("|-- Error while updating provider merchant with id {}: {}", currentMerchant.getId(), e.getMessage());
                            totalUpdateFailed++;
                        }
                    }
                }
            }

            log.info("|=> Total merchant created: {} - failed {}", totalCreate, totalCreateFailed);
            log.info("|=> Total merchant updated: {} - failed {}", totalUpdate, totalUpdateFailed);
        }
    }

    protected void checkAndDeleteProviderMerchant(String providerCode, Set<String> providerMerchantCodes) {
        try (Profiler p = new Profiler(getClass(), "checkAndDeleteProviderMerchant")) {
            log.info("|- Check and delete provider merchant for provider {} with {} merchants", providerCode, providerMerchantCodes.size());
            int batchSize = AppConst.DEFAULT_SYNC_BATCH_SIZE;
            int batchNo = 0;
            int batchCount;
            var totalDeleted = 0;
            var totalFailed = 0;
            do {
                try (Profiler p1 = new Profiler(p, "batch")) {
                    var merchantByBatch = providerMerchantService.getAllByProviderCodeAndNotInProviderMerchantCodes(
                            providerCode, providerMerchantCodes, Pageable.ofSize(batchSize)
                    ).stream().toList();

                    if (CollectionUtils.isEmpty(merchantByBatch)) {
                        log.info("|- No more merchant to delete");
                        break;
                    }
                    batchCount = merchantByBatch.size();
                    log.info("|- Batch {}", batchNo++);
                    for (var merchant : merchantByBatch) {
                        try {
                            providerMerchantService.delete(merchant.getId());
                            totalDeleted++;
                        } catch (Exception e) {
                            log.error("|-- Error while deleting provider merchant with id {}: {}", merchant.getId(), e.getMessage());
                            totalFailed++;
                        }
                    }
                }
            } while (batchCount == batchSize);

            log.info("|=> Total merchant deleted: {} - failed {}", totalDeleted, totalFailed);
        }
    }

    private boolean checkIfMerchantNeedUpdate(ProviderMerchant currentMerchant, ProviderMerchantDto latestMerchant) {
        return !Objects.equals(currentMerchant.getDisplayNames(), latestMerchant.getDisplayNames());
    }

    protected void checkAndUpdateOrCreateProviderVoucherListingByBatch(String providerCode, List<ProviderVoucherListingDto> providerVoucherListingByBatch) {
        try (Profiler p = new Profiler(getClass(), "checkAndUpdateOrCreateProviderVoucherListingByBatch")) {
            if (CollectionUtils.isEmpty(providerVoucherListingByBatch)) {
                log.info("|- No provider voucher listing to sync");
                return;
            }

            log.info("|- Check and update or create provider voucher listing by batch for provider {} with {} voucher listings", providerCode, providerVoucherListingByBatch.size());

            var providerVoucherListingIds = providerVoucherListingByBatch.stream()
                    .map(ProviderVoucherListingDto::getProviderVoucherListingId)
                    .collect(Collectors.toSet());

            // get voucher listing from db by batch
            var voucherListingByBatch = providerVoucherListingService.filter(
                    ProviderVoucherListingFilterDto.builder()
                            .providerCode(providerCode)
                            .providerVoucherListingIds(providerVoucherListingIds)
                            .status(null).build(),
                    Pageable.unpaged()
            ).stream().toList();

            var totalCreate = 0;
            var totalUpdate = 0;
            var totalCreateFailed = 0;
            var totalUpdateFailed =0;

            // Check update or create
            for (var latestListing : providerVoucherListingByBatch) {
                var currentListing = voucherListingByBatch.stream()
                        .filter(voucherListing -> voucherListing.getProviderVoucherListingId().equals(latestListing.getProviderVoucherListingId()))
                        .findFirst().orElse(null);
                if (Objects.isNull(currentListing)) {
                    var createDto = ProviderVoucherListingMapper.INSTANCE.toCreateDto(latestListing);
                    createDto.setProviderCode(providerCode);
                    try {
                        providerVoucherListingService.create(createDto);
                        totalCreate++;
                    } catch (Exception e) {
                        log.error("|-- Error while creating provider voucher listing with id {}: {}", createDto.getProviderVoucherListingId(), e.getMessage());
                        totalCreateFailed++;
                    }
                } else {
                    if (checkIfVoucherListingNeedUpdate(currentListing, latestListing)) {
                        var updateDto = ProviderVoucherListingMapper.INSTANCE.toUpdateDto(latestListing);
                        try {
                            providerVoucherListingService.update(currentListing.getId(), updateDto);
                            totalUpdate++;
                        } catch (Exception e) {
                            log.error("|-- Error while updating provider voucher listing with id {}: {}", currentListing.getId(), e.getMessage());
                            totalUpdateFailed++;
                        }
                    }
                }
            }

            log.info("|=> Total voucher listing created: {} - failed {}", totalCreate, totalCreateFailed);
            log.info("|=> Total voucher listing updated: {} - failed {}", totalUpdate, totalUpdateFailed);
        }
    }

    protected void checkAndDeleteProviderVoucherListing(String providerCode, Set<String> providerVoucherListingIds) {
        try (Profiler p = new Profiler(getClass(), "checkAndDeleteProviderVoucherListing")) {
            log.info("|- Check and delete provider voucher listing for provider {} with {} voucher listings", providerCode, providerVoucherListingIds.size());
            int batchSize = AppConst.DEFAULT_SYNC_BATCH_SIZE;;
            int batchNo = 0;
            int batchCount;
            var totalDeleted = 0;
            var totalFailed = 0;
            do {
                try (Profiler p1 = new Profiler(p, "batch")) {
                    var voucherListingByBatch = providerVoucherListingService.filter(
                            ProviderVoucherListingFilterDto.builder()
                                    .providerCode(providerCode)
                                    .notInProviderVoucherListingIds(providerVoucherListingIds)
                                    .status(null).build(),
                            Pageable.ofSize(batchSize)
                    ).stream().toList();

                    if (CollectionUtils.isEmpty(voucherListingByBatch)) {
                        log.info("|- No more voucher listing to delete");
                        break;
                    }
                    batchCount = voucherListingByBatch.size();
                    log.info("|- Batch {}", ++batchNo);
                    for (var voucherListing : voucherListingByBatch) {
                        try {
                            providerVoucherListingService.delete(voucherListing.getId());
                            totalDeleted++;
                        } catch (Exception e) {
                            log.error("|-- Error while deleting provider voucher listing with id {}: {}", voucherListing.getId(), e.getMessage());
                            totalFailed++;
                        }
                    }
                }
            } while (batchCount == batchSize);

            log.info("|=> Total voucher listing deleted: {} - failed {}", totalDeleted, totalFailed);
        }


    }

    private boolean checkIfVoucherListingNeedUpdate(ProviderVoucherListing currentListing, ProviderVoucherListingDto latestListing) {
        var isDiffQuantity = !Objects.equals(currentListing.getQuantity(), latestListing.getQuantity());
        var isDiffPrice = !Objects.equals(currentListing.getPrice().longValue(), latestListing.getPrice().longValue());
        var isDiffVoucherName = !Objects.equals(currentListing.getVoucherName(), latestListing.getVoucherName());
        var isDiffPromotionType = !Objects.equals(currentListing.getPromotionType(), latestListing.getPromotionType());
        var isDiffMetadata = !CustomUtil.compareJsonNodes(currentListing.getMetadata(), latestListing.getMetadata());
        return isDiffQuantity || isDiffPrice || isDiffVoucherName || isDiffPromotionType || isDiffMetadata;
    }

    protected void checkAndSyncPurchasedVoucherStatus(String providerCode) {
        log.info("|---------- START CHECK AND SYNC PURCHASED {} VOUCHER STATUS ----------|", providerCode);
        try (var p = new Profiler(getClass(), "checkAndSyncPurchasedVoucherStatus")) {
            int batchSize = AppConst.DEFAULT_SYNC_BATCH_SIZE;
            int pageNumber = 0;
            int totalUpdated = 0;
            int totalFailed = 0;

            log.info("|- Processing transaction IDs in batches of {}", batchSize);

            while (true) {
                try (var p1 = new Profiler(p, "batch")) {
                    // Get transaction IDs with active purchased vouchers in batches
                    var transactionIdsPage = voucherPurchasedService.getAllTransactionIdsWithActivePurchasedVouchers(
                        providerCode,
                        Pageable.ofSize(batchSize).withPage(pageNumber)
                    );

                    var transactionIds = transactionIdsPage.getContent();
                    if (CollectionUtils.isEmpty(transactionIds)) {
                        if (pageNumber == 0) {
                            log.info("|- No transaction IDs with active purchased vouchers to sync");
                        } else {
                            log.info("|- No more transaction IDs to process");
                        }
                        break;
                    }

                    log.info("|- Processing batch {} with {} transaction IDs", pageNumber + 1, transactionIds.size());

                    var purchasedVouchersForChangeStatus = getPurchasedVouchersForChangeStatus(transactionIds);

                    if (CollectionUtils.isEmpty(purchasedVouchersForChangeStatus)) {
                        log.info("|- No purchased voucher status change required for this batch");
                    } else {
                        int batchUpdated = 0;
                        int batchFailed = 0;

                        // save updated vouchers and send outbox event
                        for (var updatedVoucher : purchasedVouchersForChangeStatus) {
                            try {
                                voucherPurchasedService.changeVoucherStatus(updatedVoucher);
                                batchUpdated++;
                            } catch (Exception e) {
                                log.error("|-- Error while changing status of purchased voucher {}: {}", updatedVoucher.getVoucherCode(), e.getMessage(), e);
                                batchFailed++;
                            }
                        }

                        totalUpdated += batchUpdated;
                        totalFailed += batchFailed;
                        log.info("|- Batch {} completed: updated {} - failed {}", pageNumber + 1, batchUpdated, batchFailed);
                    }

                    if (!transactionIdsPage.hasNext()) {
                        break;
                    }
                    pageNumber++;
                }
            }

            log.info("|=> Total updated {} purchased voucher status successfully - failed {}", totalUpdated, totalFailed);
        }
        log.info("|---------- END CHECK AND SYNC PURCHASED {} VOUCHER STATUS ----------|", providerCode);
    }

    // Abstract methods to be implemented by subclasses
    protected abstract ProviderVoucherListingDetailsDto fetchProviderVoucherListingDetailFromApi(String providerVoucherListingId, Long voucherListingId);

    protected abstract VoucherPurchaseTransactionHistory processProviderPurchaseTransaction(VoucherPurchaseRequestHistory voucherPurchaseRequest, int requestQuantity);

    protected abstract VoucherPurchaseTransactionHistory rerunTransaction(VoucherPurchaseTransactionHistory txn);

    protected abstract List<VoucherPurchased> extractPurchasedVouchers(VoucherPurchaseTransactionHistory txn);

    protected abstract List<VoucherPurchasedStatusChangeReqDto> getPurchasedVouchersForChangeStatus(List<String> transactionIds);
}

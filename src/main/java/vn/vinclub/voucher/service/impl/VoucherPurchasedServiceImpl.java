package vn.vinclub.voucher.service.impl;

import com.fasterxml.uuid.Generators;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.event.internal.PurchasedVoucherStatusChangeEvent;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedStatusChangeReqDto;
import vn.vinclub.voucher.enums.PurchasedVoucherStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.model.VoucherPurchased;
import vn.vinclub.voucher.repository.VoucherPurchasedRepository;
import vn.vinclub.voucher.service.EventService;
import vn.vinclub.voucher.service.VoucherPurchasedService;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class VoucherPurchasedServiceImpl extends BaseService implements VoucherPurchasedService {
    private final VoucherPurchasedRepository voucherPurchasedRepository;
    private final EventService eventService;

    @Override
    public void changeVoucherStatus(VoucherPurchasedStatusChangeReqDto statusChangeReqDto) {
        TransactionStatus tx = null;
        try (var p = new Profiler(getClass(), "changeVoucherStatus")) {
            var voucherPurchased = voucherPurchasedRepository.findByTransactionIdAndVoucherCode(statusChangeReqDto.getTransactionId(), statusChangeReqDto.getVoucherCode())
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, VoucherPurchased.NAME, VoucherPurchased.Fields.transactionId, statusChangeReqDto.getTransactionId()));

            if (!voucherPurchased.getProviderCode().equals(statusChangeReqDto.getProviderCode())) {
                log.warn("Provider code mismatch: expected {}, but got {}", voucherPurchased.getProviderCode(), statusChangeReqDto.getProviderCode());
                throw new BusinessLogicException(AppErrorCode.BAD_REQUEST);
            }

            if (!PurchasedVoucherStatusEnum.ACTIVE.equals(voucherPurchased.getStatus())) {
                log.warn("VoucherPurchased with id {} is not in ACTIVE status", voucherPurchased.getId());
                throw new BusinessLogicException(AppErrorCode.BAD_REQUEST);
            }

            if (statusChangeReqDto.getStatus().equals(voucherPurchased.getStatus())) {
                log.warn("Request to change status of VoucherPurchased with id {} to the same status", voucherPurchased.getId());
                throw new BusinessLogicException(AppErrorCode.BAD_REQUEST);
            }

            var outboxId = Generators.timeBasedEpochGenerator().generate().toString();
            // initiate a new transaction
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
            tx = transactionManager.getTransaction(def);

            voucherPurchased.setStatus(statusChangeReqDto.getStatus());
            voucherPurchased.setVoucherRedeemedTime(statusChangeReqDto.getVoucherRedeemedTime());
            voucherPurchasedRepository.save(voucherPurchased);

            // Persist the outbox event
            eventService.persistOutboxEvent(
                    PurchasedVoucherStatusChangeEvent.builder()
                            .voucherImportId(voucherPurchased.getVoucherImportId())
                            .providerCode(voucherPurchased.getProviderCode())
                            .voucherListingId(voucherPurchased.getVoucherListingId())
                            .transactionId(voucherPurchased.getTransactionId())
                            .providerTransactionId(voucherPurchased.getProviderTransactionId())
                            .voucherCode(voucherPurchased.getVoucherCode())
                            .oldStatus(PurchasedVoucherStatusEnum.ACTIVE)
                            .newStatus(voucherPurchased.getStatus())
                            .redeemedTime(voucherPurchased.getVoucherRedeemedTime())
                            .build(),
                    outboxId
            );
            transactionManager.commit(tx);

            // Send outbox event
            try {
                eventService.sendOutboxEvent(outboxId, null);
            } catch (Exception e) {
                log.error("Failed to send outbox event with id {}", outboxId, e);
                throw new BusinessLogicException(AppErrorCode.INTERNAL_SERVER_ERROR, e);
            }

        }
    }

    @Override
    public List<VoucherPurchased> saveMultiple(List<VoucherPurchased> voucherPurchasedList) {
        try (var p = new Profiler(getClass(), "saveMultiple")) {
            return voucherPurchasedRepository.saveAll(voucherPurchasedList);
        }
    }

    @Override
    public Page<VoucherPurchased> getAllByVoucherImportId(String voucherImportId, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getAllByVoucherImportId")) {
            return voucherPurchasedRepository.findAllByVoucherImportId(voucherImportId, pageable);
        }
    }

    @Override
    public Page<String> getAllTransactionIdsWithActivePurchasedVouchers(String providerCode, Pageable pageable) {
        try (var p = new Profiler(getClass(), "getAllTransactionIdsWithActivePurchasedVouchers")) {
            return voucherPurchasedRepository.findUniqueTransactionIdsByStatus(providerCode, PurchasedVoucherStatusEnum.ACTIVE, pageable);
        }
    }
}

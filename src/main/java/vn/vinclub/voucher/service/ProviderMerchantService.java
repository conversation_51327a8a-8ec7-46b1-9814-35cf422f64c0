package vn.vinclub.voucher.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantCreateDto;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantUpdateDto;
import vn.vinclub.voucher.model.ProviderMerchant;

import java.util.List;
import java.util.Set;

public interface ProviderMerchantService {

    ProviderMerchant create(ProviderMerchantCreateDto createDto);

    ProviderMerchant update(Long id, ProviderMerchantUpdateDto updateDto);

    boolean delete(Long id);

    ProviderMerchant getById(Long id);

    Page<ProviderMerchant> getAllByProviderId(Long providerId, Pageable pageable);

    Page<ProviderMerchant> getAllByProviderCode(String providerCode, Pageable pageable);

    List<ProviderMerchant> getAllByProviderCodeAndProviderMerchantCodes(String providerCode, Set<String> providerMerchantCodes);

    Page<ProviderMerchant> getAllByProviderCodeAndNotInProviderMerchantCodes(String providerCode, Set<String> providerMerchantCodes, Pageable pageable);

    String lookupByVClubCode(String providerCode, String vclubMerchantCode);

    void invalidateCache(Long id);
}

package vn.vinclub.voucher.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vinclub.voucher.model.ProviderMerchant;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ProviderMerchantRepository extends JpaRepository<ProviderMerchant, Long> {

    boolean existsByProviderCodeAndProviderMerchantCodeAndActive(String providerCode, String providerMerchantCode, Boolean active);

    Optional<ProviderMerchant> findByIdAndActive(Long id, boolean active);

    List<ProviderMerchant> findAllByActive(boolean active);

    Page<ProviderMerchant> findByProviderCodeAndActive(String providerCode, boolean active, Pageable pageable);

    Optional<ProviderMerchant> findByProviderCodeAndVclubMerchantCodeAndActive(String providerCode, String vclubMerchantCode, boolean active);

    List<ProviderMerchant> findByProviderCodeAndProviderMerchantCodeInAndActive(String providerCode, Set<String> providerMerchantCodes, boolean active);

    @Query("SELECT pm FROM ProviderMerchant pm WHERE pm.providerCode = :providerCode AND pm.providerMerchantCode NOT IN :providerMerchantCodes AND pm.active = true")
    Page<ProviderMerchant> findByProviderCodeAndProviderMerchantCodeNotInAndActive(@Param("providerCode") String providerCode, @Param("providerMerchantCodes") Set<String> providerMerchantCodes, Pageable pageable);
}

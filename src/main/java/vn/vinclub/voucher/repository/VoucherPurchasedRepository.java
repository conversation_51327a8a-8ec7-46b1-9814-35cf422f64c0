package vn.vinclub.voucher.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vinclub.voucher.enums.PurchasedVoucherStatusEnum;
import vn.vinclub.voucher.model.VoucherPurchased;

import java.util.Optional;

@Repository
public interface VoucherPurchasedRepository extends JpaRepository<VoucherPurchased, Long> {

    Page<VoucherPurchased> findAllByVoucherImportId(String voucherImportId, Pageable pageable);

    @Query("SELECT DISTINCT vp.transactionId FROM VoucherPurchased vp WHERE vp.providerCode=:providerCode AND vp.status = :status AND vp.active = true")
    Page<String> findUniqueTransactionIdsByStatus(String providerCode, PurchasedVoucherStatusEnum status, Pageable pageable);

    @Query("SELECT vp FROM VoucherPurchased vp WHERE vp.transactionId = :transactionId AND vp.voucherCode = :voucherCode AND vp.active = true")
    Optional<VoucherPurchased> findByTransactionIdAndVoucherCode(String transactionId, String voucherCode);
}
